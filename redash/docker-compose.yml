services:
  postgres:
    image: postgres:13-alpine
    environment:
      POSTGRES_USER: redash
      POSTGRES_PASSWORD: redash
      POSTGRES_DB: redash
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U redash"]
      interval: 30s
      timeout: 10s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6381:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

  redash:
    image: redash/redash:latest
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "5000:5000"
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
      REDASH_WEB_WORKERS: 4
      REDASH_ALLOW_PRIVATE_ADDRESSES: "true"
      REDASH_ENFORCE_PRIVATE_IP_BLOCK: "false"
      # Allow API access from localhost and host network
      REDASH_CORS_ACCESS_CONTROL_ALLOW_ORIGIN: "http://localhost:3000,http://*************:3000"
      REDASH_CORS_ACCESS_CONTROL_ALLOW_CREDENTIALS: "true"
    restart: unless-stopped
    command: server
    volumes:
      - redash_data:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  api:
    build:
      context: ../api
    image: arca/cosmosdb-redash-api:local
    environment:
      - NODE_ENV=development
      - PORT=3000
      - ALLOWED_ORIGINS=http://localhost:5000
      - API_KEY=test-key
    ports:
      - "3000:3000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 15s
      timeout: 5s
      retries: 5

  redash_scheduler:
    image: redash/redash:latest
    depends_on:
      redash:
        condition: service_healthy
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
      REDASH_ALLOW_PRIVATE_ADDRESSES: "true"
      REDASH_ENFORCE_PRIVATE_IP_BLOCK: "false"  # Added for consistency
    restart: unless-stopped
    command: scheduler
    volumes:
      - redash_data:/app

  redash_scheduled_worker:
    image: redash/redash:latest
    depends_on:
      redash:
        condition: service_healthy
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
      REDASH_ALLOW_PRIVATE_ADDRESSES: "true"
      REDASH_ENFORCE_PRIVATE_IP_BLOCK: "false"  # Added for consistency
      QUEUES: "scheduled_queries,schemas"
    restart: unless-stopped
    command: worker
    volumes:
      - redash_data:/app

  redash_adhoc_worker:
    image: redash/redash:latest
    depends_on:
      redash:
        condition: service_healthy
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
      REDASH_ALLOW_PRIVATE_ADDRESSES: "true"
      REDASH_ENFORCE_PRIVATE_IP_BLOCK: "false"
      QUEUES: "queries"
    restart: unless-stopped
    command: worker
    volumes:
      - redash_data:/app

volumes:
  postgres_data:
  redash_data:

networks:
  default:
    name: redash_network
